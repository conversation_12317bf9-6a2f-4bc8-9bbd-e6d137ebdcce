import { useTranslation } from 'react-i18next';
import { GraduationCap, Briefcase, Users } from 'lucide-react';

export default function TargetAudienceSection() {
  const { t } = useTranslation();

  const audiences = [
    {
      icon: GraduationCap,
      title: t('students'),
      description: t('studentsDescription'),
      emoji: '🎓'
    },
    {
      icon: Briefcase,
      title: t('professionals'),
      description: t('professionalsDescription'),
      emoji: '💼'
    },
    {
      icon: Users,
      title: t('everydayUsers'),
      description: t('everydayUsersDescription'),
      emoji: '🏠'
    }
  ];

  return (
    <section className="bg-white py-8 sm:py-12">
      <div className="max-w-6xl mx-auto px-3 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-3 sm:mb-4">
            👤 {t('targetAudience')}
          </h2>
          <p className="text-muted-foreground text-sm sm:text-base max-w-2xl mx-auto">
            {t('targetAudienceDescription', 'AI Notes is designed for everyone who needs a reliable, easy-to-use note-taking solution')}
          </p>
        </div>

        {/* Audience Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          {audiences.map((audience, index) => {
            const IconComponent = audience.icon;
            return (
              <div
                key={index}
                className="bg-gray-50 rounded-lg p-4 sm:p-6 border border-gray-200 hover:shadow-md transition-shadow"
              >
                {/* Icon and Title */}
                <div className="flex items-center space-x-3 mb-3 sm:mb-4">
                  <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-lg">
                    <span className="text-lg sm:text-xl">{audience.emoji}</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground text-base sm:text-lg">
                      {audience.title}
                    </h3>
                  </div>
                </div>

                {/* Description */}
                <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">
                  {audience.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-6 sm:mt-8">
          <p className="text-muted-foreground text-sm sm:text-base">
            {t('startUsingNow', 'Start using AI Notes now - no registration required!')}
          </p>
        </div>
      </div>
    </section>
  );
}
