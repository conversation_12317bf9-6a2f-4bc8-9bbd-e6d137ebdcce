<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Debug</title>
</head>
<body>
    <h1>🎉 Language Switch Fix Verification</h1>
    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>✅ 修复完成</h3>
        <p><strong>问题原因：</strong></p>
        <ul>
            <li>路由配置中使用了 &lt;div&gt; 包装 Route 组件，导致 wouter 路由失效</li>
            <li>SEOHead 组件在服务端渲染时访问 window 对象导致错误</li>
            <li>i18n 初始化时机问题</li>
        </ul>
        <p><strong>修复方案：</strong></p>
        <ul>
            <li>✅ 移除路由包装的 div 元素，直接使用 Route 组件</li>
            <li>✅ 在 SEOHead 组件中添加客户端环境检查</li>
            <li>✅ 优化 i18n 初始化逻辑</li>
        </ul>
    </div>
    <div id="results"></div>

    <script>
        async function testUrls() {
            const urls = [
                'http://localhost:5000/',
                'http://localhost:5000/zh/',
                'http://localhost:5000/es/',
            ];

            const results = document.getElementById('results');

            for (const url of urls) {
                try {
                    const response = await fetch(url);
                    const text = await response.text();

                    results.innerHTML += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ccc;">
                            <h3>${url}</h3>
                            <p>Status: ${response.status}</p>
                            <p>Content Length: ${text.length}</p>
                            <p>Contains React Root: ${text.includes('id="root"') ? 'Yes' : 'No'}</p>
                            <p>Contains Error: ${text.includes('error') ? 'Yes' : 'No'}</p>
                            <details>
                                <summary>First 500 chars</summary>
                                <pre>${text.substring(0, 500)}</pre>
                            </details>
                        </div>
                    `;
                } catch (error) {
                    results.innerHTML += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid red;">
                            <h3>${url}</h3>
                            <p style="color: red;">Error: ${error.message}</p>
                        </div>
                    `;
                }
            }
        }

        testUrls();
    </script>
</body>
</html>
