import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'wouter';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SEOHead from '@/components/seo-head';

export default function PrivacyPolicy() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      <SEOHead
        title={t('privacyPolicy')}
        description={t('privacyContent', 'Learn about how AI Notes protects your privacy and handles your data. All notes are stored locally in your browser.')}
        path="/privacy-policy"
      />
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16">
            <div className="flex items-center space-x-2 sm:space-x-4 min-w-0">
              <Link href="/">
                <Button variant="ghost" size="sm" className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3">
                  <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="text-xs sm:text-sm">Back</span>
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <img src="/ainotes-logo-192.png" alt="AiNotes Logo" className="h-5 w-5 sm:h-6 sm:w-6" />
                <span className="text-base sm:text-lg font-bold text-foreground truncate">{t('appName')}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-12">
        <div className="prose prose-sm sm:prose-lg max-w-none">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-6 sm:mb-8">{t('privacyPolicy')}</h1>

          <div className="text-muted-foreground mb-6 sm:mb-8">
            <p className="text-base sm:text-lg leading-relaxed">{t('privacyContent')}</p>
          </div>

          <div className="space-y-6 sm:space-y-8">
            <section>
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground mb-3 sm:mb-4">{t('privacyDataCollection')}</h2>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">{t('privacyDataCollectionText')}</p>
            </section>

            <section>
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground mb-3 sm:mb-4">{t('privacyDataUse')}</h2>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">{t('privacyDataUseText')}</p>
            </section>

            <section>
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground mb-3 sm:mb-4">{t('privacyDataSecurity')}</h2>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">{t('privacyDataSecurityText')}</p>
            </section>

            <section>
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground mb-3 sm:mb-4">{t('privacyContact')}</h2>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">{t('privacyContactText')}</p>
            </section>
          </div>

          <div className="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-gray-200">
            <p className="text-xs sm:text-sm text-muted-foreground">
              © 2025 {t('appName')}. {t('allRightsReserved')}
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}