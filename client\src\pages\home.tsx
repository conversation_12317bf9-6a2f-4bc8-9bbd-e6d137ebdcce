import { useState, useEffect } from 'react';
import { useParams } from 'wouter';
import { useTranslation } from 'react-i18next';
import Header from '@/components/header';
import Toolbar from '@/components/toolbar';
import Editor from '@/components/editor';
import Footer from '@/components/footer';
import SEOHead from '@/components/seo-head';
import FindReplaceModal from '@/components/modals/find-replace-modal';
import FAQSection from '@/components/faq-section';
import TargetAudienceSection from '@/components/target-audience-section';

import { LocalStorage } from '@/lib/storage';
import { useAutoSave } from '@/hooks/use-autosave';
import { useWordCount } from '@/hooks/use-word-count';

export default function Home() {
  const { shareId } = useParams<{ shareId?: string }>();
  const { t } = useTranslation();

  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error'>('saved');
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);
  const [isFindReplaceOpen, setIsFindReplaceOpen] = useState(false);


  const wordCount = useWordCount(content);

  useAutoSave({
    content,
    title,
    onSave: (status) => {
      setSaveStatus(status);
      if (status === 'saved') {
        setLastSavedTime(new Date());
      }
    }
  });

  // Load saved content on mount
  useEffect(() => {
    const savedNote = LocalStorage.loadNote();
    if (savedNote) {
      setContent(savedNote.content);
      setTitle(savedNote.title || '');
      setLastSavedTime(new Date(savedNote.timestamp));
    }
  }, []);

  const handleSaveNote = () => {
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `note-${new Date().toISOString().split('T')[0]}.html`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${title || 'AI Notes Document'}</title>
            <style>
              body { font-family: Inter, system-ui, sans-serif; line-height: 1.6; margin: 2rem; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>${content}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleNewDocument = () => {
    if (content.trim() && !confirm(t('confirmNewDocument'))) {
      return;
    }
    setContent('');
    setTitle('');
    LocalStorage.clearNote();
    setSaveStatus('saved');
    setLastSavedTime(null);
  };

  const handleInsertDateTime = () => {
    const dateTime = new Date().toLocaleString();
    setContent(prev => prev + ' ' + dateTime);
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <SEOHead
        title={title || t('appName')}
        description={t('metaDescription', 'AI Notes is a powerful, free online note-taking application with rich text editing, auto-save, and multi-language support. Create, edit, and share notes instantly.')}
        path="/"
      />
      <Header
        saveStatus={saveStatus}
        lastSavedTime={lastSavedTime}
      />

      <Toolbar
        wordCount={wordCount.words}
        onNew={handleNewDocument}
        onSave={handleSaveNote}
        onPrint={handlePrint}
        onFindReplace={() => setIsFindReplaceOpen(true)}
        onInsertDateTime={handleInsertDateTime}
      />

      <main className="flex-1 bg-white">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-6">
          <Editor
            content={content}
            onChange={setContent}
            placeholder={t('editorPlaceholder')}
          />

          <div className="flex flex-col sm:flex-row sm:items-center justify-between mt-3 sm:mt-4 text-xs sm:text-sm text-muted-foreground gap-2 sm:gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0">
              <span className="whitespace-nowrap">
                {t('lastSaved')}: {lastSavedTime ? t('justNow') : '—'}
              </span>
              <span className="whitespace-nowrap">
                {t('characters')}: {wordCount.characters}
              </span>
            </div>
          </div>
        </div>
      </main>

      {/* FAQ and Target Audience Sections */}
      <FAQSection />
      <TargetAudienceSection />

      <Footer />

      <FindReplaceModal
        isOpen={isFindReplaceOpen}
        onClose={() => setIsFindReplaceOpen(false)}
        content={content}
        onContentChange={setContent}
      />


    </div>
  );
}
