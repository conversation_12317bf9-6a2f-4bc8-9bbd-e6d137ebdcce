export interface NoteData {
  content: string;
  title?: string;
  timestamp: string;
}

export class LocalStorage {
  private static readonly CONTENT_KEY = 'ainotes_content';
  private static readonly TIMESTAMP_KEY = 'ainotes_timestamp';
  private static readonly TITLE_KEY = 'ainotes_title';

  static saveNote(data: NoteData): void {
    try {
      localStorage.setItem(this.CONTENT_KEY, data.content);
      localStorage.setItem(this.TIMESTAMP_KEY, data.timestamp);
      if (data.title) {
        localStorage.setItem(this.TITLE_KEY, data.title);
      }
    } catch (error) {
      console.error('Failed to save note to localStorage:', error);
      throw new Error('Failed to save note. Storage might be full.');
    }
  }

  static loadNote(): NoteData | null {
    try {
      const content = localStorage.getItem(this.CONTENT_KEY);
      const timestamp = localStorage.getItem(this.TIMESTAMP_KEY);
      const title = localStorage.getItem(this.TITLE_KEY);

      if (!content) return null;

      return {
        content,
        title: title || undefined,
        timestamp: timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to load note from localStorage:', error);
      return null;
    }
  }

  static clearNote(): void {
    try {
      localStorage.removeItem(this.CONTENT_KEY);
      localStorage.removeItem(this.TIMESTAMP_KEY);
      localStorage.removeItem(this.TITLE_KEY);
    } catch (error) {
      console.error('Failed to clear note from localStorage:', error);
    }
  }

  static generateShareId(): string {
    return Math.random().toString(36).substr(2, 10);
  }

  static getShareUrl(shareId: string): string {
    return `${window.location.origin}/note/${shareId}`;
  }
}
