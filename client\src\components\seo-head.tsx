import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { detectLanguageFromPath, getLanguagePrefix, supportedLanguages } from '@/lib/i18n';

interface SEOHeadProps {
  title?: string;
  description?: string;
  path?: string;
}

export default function SEOHead({ title, description, path = '/' }: SEOHeadProps) {
  const { t, i18n } = useTranslation();

  // 安全地获取当前语言
  const getCurrentLang = () => {
    if (typeof window === 'undefined') return 'en';
    const pathname = window.location.pathname;
    const langMatch = pathname.match(/^\/([a-z]{2})\//);
    if (langMatch && supportedLanguages.some(lang => lang.code === langMatch[1])) {
      return langMatch[1];
    }
    return 'en';
  };

  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;

    const currentLang = getCurrentLang();
    // 动态更新页面标题
    const pageTitle = title || t('appName');
    const fullTitle = `${pageTitle} - ${t('subtitle')}`;
    document.title = fullTitle;

    // 更新meta描述
    const metaDescription = description || t('metaDescription', 'AI Notes is a powerful, free online note-taking application with rich text editing, auto-save, and multi-language support. Create, edit, and share notes instantly.');
    let descriptionMeta = document.querySelector('meta[name="description"]');
    if (!descriptionMeta) {
      descriptionMeta = document.createElement('meta');
      descriptionMeta.setAttribute('name', 'description');
      document.head.appendChild(descriptionMeta);
    }
    descriptionMeta.setAttribute('content', metaDescription);

    // 更新语言属性
    document.documentElement.lang = currentLang;

    // 更新Open Graph标签
    updateOpenGraphTags(fullTitle, metaDescription, currentLang, path);

    // 更新hreflang标签
    updateHreflangTags(path);

    // 更新canonical标签
    updateCanonicalTag(currentLang, path);

  }, [t, i18n.language, title, description, path]);

  return null; // 这个组件不渲染任何内容
}

function updateOpenGraphTags(title: string, description: string, currentLang: string, path: string) {
  const ogTags = [
    { property: 'og:title', content: title },
    { property: 'og:description', content: description },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: `https://ainotes.work${getLanguagePrefix(currentLang)}${path}` },
    { property: 'og:image', content: 'https://ainotes.work/ainotes-logo-512.png' },
    { property: 'og:image:width', content: '512' },
    { property: 'og:image:height', content: '512' },
    { property: 'og:image:alt', content: 'AI Notes Logo' },
    { property: 'og:locale', content: getLocaleCode(currentLang) },
    { property: 'og:site_name', content: 'AI Notes' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
    { name: 'twitter:image', content: 'https://ainotes.work/ainotes-logo-512.png' },
  ];

  ogTags.forEach(tag => {
    const selector = tag.property ? `meta[property="${tag.property}"]` : `meta[name="${tag.name}"]`;
    let meta = document.querySelector(selector);
    if (!meta) {
      meta = document.createElement('meta');
      if (tag.property) {
        meta.setAttribute('property', tag.property);
      } else {
        meta.setAttribute('name', tag.name!);
      }
      document.head.appendChild(meta);
    }
    meta.setAttribute('content', tag.content);
  });

  // 添加其他语言的og:locale:alternate
  supportedLanguages.forEach(langObj => {
    if (langObj.code !== currentLang) {
      const altLocaleMeta = document.querySelector(`meta[property="og:locale:alternate"][content="${getLocaleCode(langObj.code)}"]`);
      if (!altLocaleMeta) {
        const meta = document.createElement('meta');
        meta.setAttribute('property', 'og:locale:alternate');
        meta.setAttribute('content', getLocaleCode(langObj.code));
        document.head.appendChild(meta);
      }
    }
  });
}

function updateHreflangTags(path: string) {
  // 移除现有的hreflang标签
  document.querySelectorAll('link[rel="alternate"][hreflang]').forEach(link => link.remove());

  // 添加新的hreflang标签
  supportedLanguages.forEach(lang => {
    const link = document.createElement('link');
    link.rel = 'alternate';
    link.hreflang = getHreflangCode(lang.code);
    link.href = `https://ainotes.work${getLanguagePrefix(lang.code)}${path}`;
    document.head.appendChild(link);
  });

  // 添加x-default
  const defaultLink = document.createElement('link');
  defaultLink.rel = 'alternate';
  defaultLink.hreflang = 'x-default';
  defaultLink.href = `https://ainotes.work${path}`;
  document.head.appendChild(defaultLink);
}

function updateCanonicalTag(lang: string, path: string) {
  let canonical = document.querySelector('link[rel="canonical"]');
  if (!canonical) {
    canonical = document.createElement('link');
    canonical.setAttribute('rel', 'canonical');
    document.head.appendChild(canonical);
  }
  canonical.setAttribute('href', `https://ainotes.work${getLanguagePrefix(lang)}${path}`);
}

function getLocaleCode(lang: string): string {
  const localeMap: Record<string, string> = {
    'en': 'en_US',
    'es': 'es_ES',
    'fr': 'fr_FR',
    'de': 'de_DE',
    'zh': 'zh_CN',
  };
  return localeMap[lang] || 'en_US';
}

function getHreflangCode(lang: string): string {
  const hreflangMap: Record<string, string> = {
    'en': 'en-US',
    'es': 'es-ES',
    'fr': 'fr-FR',
    'de': 'de-DE',
    'zh': 'zh-CN',
  };
  return hreflangMap[lang] || 'en-US';
}
