@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 31 6% 17%; /* #1F2937 */
  --muted: 210 11% 96%; /* #F9FAFB */
  --muted-foreground: 220 9% 46%; /* #6B7280 */
  --popover: 0 0% 100%;
  --popover-foreground: 31 6% 17%;
  --card: 0 0% 100%;
  --card-foreground: 31 6% 17%;
  --border: 214 32% 91%; /* #E5E7EB */
  --input: 214 32% 91%;
  --primary: 217 91% 60%; /* #2563EB */
  --primary-foreground: 0 0% 100%;
  --secondary: 210 11% 96%;
  --secondary-foreground: 31 6% 17%;
  --accent: 210 11% 96%;
  --accent-foreground: 31 6% 17%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --ring: 217 91% 60%;
  --radius: 0.5rem;
}

.dark {
  --background: 224 71% 4%;
  --foreground: 0 0% 98%;
  --muted: 223 47% 11%;
  --muted-foreground: 215 16% 57%;
  --popover: 224 71% 4%;
  --popover-foreground: 0 0% 98%;
  --card: 224 71% 4%;
  --card-foreground: 0 0% 98%;
  --border: 216 34% 17%;
  --input: 216 34% 17%;
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 100%;
  --secondary: 223 47% 11%;
  --secondary-foreground: 0 0% 98%;
  --accent: 223 47% 11%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 0 0% 98%;
  --ring: 217 91% 60%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
}

/* Summernote Editor Customization */
.note-editor {
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 8px !important;
  font-family: 'Inter', system-ui, sans-serif !important;
  width: 100% !important;
  max-width: 100% !important;
}

.note-toolbar {
  background: hsl(var(--muted)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
  padding: 8px 12px !important;
  border-radius: 8px 8px 0 0 !important;
  white-space: nowrap !important;
}

.note-editing-area {
  min-height: 400px !important;
}

.note-editable {
  padding: 16px !important;
  font-family: 'Inter', system-ui, sans-serif !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
  color: hsl(var(--foreground)) !important;
  background: hsl(var(--background)) !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.note-editable:focus {
  outline: none !important;
}

.dropdown-menu {
  border-radius: 8px !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  background: hsl(var(--background)) !important;
  max-width: 90vw !important;
}

.btn-group .btn {
  border-radius: 6px !important;
  margin-right: 2px !important;
  font-family: 'Inter', system-ui, sans-serif !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

.btn-group .btn:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .note-toolbar {
    padding: 6px 8px !important;
    font-size: 12px !important;
  }
  
  .note-editing-area {
    min-height: 300px !important;
  }
  
  .note-editable {
    padding: 12px !important;
    font-size: 16px !important;
    -webkit-text-size-adjust: 100% !important;
  }
  
  .btn-group .btn {
    font-size: 11px !important;
    padding: 3px 6px !important;
    margin-right: 1px !important;
  }
  
  .dropdown-menu {
    font-size: 14px !important;
    max-width: 95vw !important;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .note-editable {
    -webkit-overflow-scrolling: touch !important;
  }
}

.status-indicator {
  transition: all 0.3s ease;
}

.save-animation {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.5);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Fix Summernote dropdown z-index issues */
.note-dropdown-menu {
  z-index: 9999 !important;
  position: absolute !important;
}

.note-color-palette {
  z-index: 9999 !important;
}

.note-table-popover {
  z-index: 9999 !important;
}

.note-para-popover {
  z-index: 9999 !important;
}

.dropdown-menu {
  z-index: 9999 !important;
}

.note-popover {
  z-index: 9999 !important;
}

.note-popover .popover-content {
  z-index: 9999 !important;
}
