import fs from 'fs';
import path from 'path';

console.log('🚀 Running deployment checks...\n');

const checks = [];

// 检查必要的文件是否存在
function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  checks.push({
    name: `File exists: ${description}`,
    status: exists ? 'PASS' : 'FAIL',
    details: exists ? `✅ ${filePath}` : `❌ Missing: ${filePath}`
  });
  return exists;
}

// 检查文件内容
function checkFileContent(filePath, description, validator) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const isValid = validator(content);
    checks.push({
      name: `Content check: ${description}`,
      status: isValid ? 'PASS' : 'FAIL',
      details: isValid ? `✅ ${description} is valid` : `❌ ${description} validation failed`
    });
    return isValid;
  } catch (error) {
    checks.push({
      name: `Content check: ${description}`,
      status: 'FAIL',
      details: `❌ Error reading ${filePath}: ${error.message}`
    });
    return false;
  }
}

// 1. 检查构建文件
console.log('1. Checking build files...');
checkFileExists('dist/public/index.html', 'Built HTML file');
checkFileExists('dist/index.js', 'Built server file');

// 2. 检查SEO文件
console.log('2. Checking SEO files...');
checkFileExists('client/public/sitemap.xml', 'Sitemap');
checkFileExists('client/public/robots.txt', 'Robots.txt');
checkFileExists('client/public/manifest.json', 'Web App Manifest');

// 3. 检查多语言支持文件
console.log('3. Checking multi-language support...');
checkFileExists('client/src/lib/i18n.ts', 'i18n configuration');
checkFileExists('client/src/components/seo-head.tsx', 'SEO Head component');

// 4. 检查Vercel配置
console.log('4. Checking Vercel configuration...');
checkFileContent('vercel.json', 'Vercel config', (content) => {
  const config = JSON.parse(content);
  return config.rewrites && config.rewrites.length > 0;
});

// 5. 检查sitemap.xml内容
console.log('5. Checking sitemap.xml content...');
checkFileContent('client/public/sitemap.xml', 'Sitemap XML', (content) => {
  return content.includes('https://ainotes.work') && 
         content.includes('hreflang') &&
         content.includes('/zh/') &&
         content.includes('/es/');
});

// 6. 检查robots.txt内容
console.log('6. Checking robots.txt content...');
checkFileContent('client/public/robots.txt', 'Robots.txt', (content) => {
  return content.includes('User-agent: *') && 
         content.includes('Sitemap: https://ainotes.work/sitemap.xml');
});

// 7. 检查manifest.json内容
console.log('7. Checking manifest.json content...');
checkFileContent('client/public/manifest.json', 'Web App Manifest', (content) => {
  const manifest = JSON.parse(content);
  return manifest.name && manifest.short_name && manifest.icons && manifest.icons.length > 0;
});

// 8. 检查package.json脚本
console.log('8. Checking package.json scripts...');
checkFileContent('package.json', 'Package.json scripts', (content) => {
  const pkg = JSON.parse(content);
  return pkg.scripts && 
         pkg.scripts['generate-seo'] && 
         pkg.scripts['vercel-build'] &&
         pkg.scripts['vercel-build'].includes('generate-seo');
});

// 9. 检查TypeScript配置
console.log('9. Checking TypeScript configuration...');
checkFileExists('tsconfig.json', 'TypeScript config');

// 10. 检查语言文件完整性
console.log('10. Checking language files...');
checkFileContent('client/src/lib/i18n.ts', 'Language resources', (content) => {
  return content.includes('supportedLanguages') &&
         content.includes('detectLanguageFromPath') &&
         content.includes('generateLanguageUrl') &&
         content.includes('en:') &&
         content.includes('zh:') &&
         content.includes('es:');
});

// 输出结果
console.log('\n📊 Deployment Check Results:');
console.log('=' .repeat(50));

let passCount = 0;
let failCount = 0;

checks.forEach(check => {
  console.log(`${check.status === 'PASS' ? '✅' : '❌'} ${check.name}`);
  if (check.details) {
    console.log(`   ${check.details}`);
  }
  
  if (check.status === 'PASS') {
    passCount++;
  } else {
    failCount++;
  }
});

console.log('\n' + '=' .repeat(50));
console.log(`📈 Summary: ${passCount} passed, ${failCount} failed`);

if (failCount === 0) {
  console.log('🎉 All checks passed! Ready for deployment.');
  process.exit(0);
} else {
  console.log('⚠️  Some checks failed. Please fix the issues before deploying.');
  process.exit(1);
}
