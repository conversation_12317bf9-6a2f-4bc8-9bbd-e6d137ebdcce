import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

interface EditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
}

declare global {
  interface Window {
    $: any;
  }
}

export default function Editor({ content, onChange, placeholder }: EditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const isInitialized = useRef(false);

  useEffect(() => {
    if (!editorRef.current || isInitialized.current || !window.$) return;

    const $editor = window.$(editorRef.current);
    
    $editor.summernote({
      height: 500,
      minHeight: 400,
      maxHeight: 800,
      placeholder: placeholder || t('editorPlaceholder'),
      toolbar: [
        ['font', ['bold', 'italic', 'underline', 'strikethrough', 'clear']],
        ['fontname', ['fontname']],
        ['fontsize', ['fontsize']],
        ['color', ['color']],
        ['para', ['paragraph']],
        ['table', ['table']],
        ['insert', ['link', 'hr']],
        ['view', ['fullscreen', 'codeview', 'help']]
      ],
      fontNames: [
        'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 
        'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 
        'Verdana', 'Inter'
      ],
      fontNamesIgnoreCheck: ['Inter'],
      callbacks: {
        onChange: function(contents: string) {
          onChange(contents);
        },
        onInit: function() {
          // Set initial content
          $editor.summernote('code', content);
        }
      }
    });

    isInitialized.current = true;

    // Cleanup function
    return () => {
      if (isInitialized.current && window.$) {
        try {
          $editor.summernote('destroy');
          isInitialized.current = false;
        } catch (error) {
          console.warn('Error destroying Summernote:', error);
        }
      }
    };
  }, []);

  // Update content when prop changes
  useEffect(() => {
    if (isInitialized.current && editorRef.current && window.$) {
      const $editor = window.$(editorRef.current);
      const currentContent = $editor.summernote('code');
      if (currentContent !== content) {
        $editor.summernote('code', content);
      }
    }
  }, [content]);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div ref={editorRef} />
    </div>
  );
}
