import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Copy, Check } from 'lucide-react';
import { FaFacebookF, FaTwitter, FaWhatsapp } from 'react-icons/fa';
import { LocalStorage } from '@/lib/storage';
import { useToast } from '@/hooks/use-toast';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  title?: string;
}

export default function ShareModal({
  isOpen,
  onClose,
  content,
  title
}: ShareModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);

  const shareUrl = useMemo(() => {
    const shareId = LocalStorage.generateShareId();
    return LocalStorage.getShareUrl(shareId);
  }, [isOpen]);

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast({
        title: t('copied'),
        description: 'Share link copied to clipboard',
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive',
      });
    }
  };

  const handleSocialShare = (platform: 'facebook' | 'twitter' | 'whatsapp') => {
    const text = title || 'Check out my note on AI Notes';
    const url = shareUrl;
    
    let shareUrlFormatted = '';
    
    switch (platform) {
      case 'facebook':
        shareUrlFormatted = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrlFormatted = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'whatsapp':
        shareUrlFormatted = `https://wa.me/?text=${encodeURIComponent(`${text} ${url}`)}`;
        break;
    }
    
    window.open(shareUrlFormatted, '_blank', 'width=600,height=400');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('shareNote')}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="share-url" className="text-sm font-medium">
              {t('shareLink')}
            </Label>
            <div className="flex items-center space-x-2 mt-1">
              <Input
                id="share-url"
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 bg-muted"
              />
              <Button
                size="sm"
                onClick={handleCopyUrl}
                className="flex items-center space-x-1"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-4 pt-4">
            <Button
              variant="outline"
              onClick={() => handleSocialShare('facebook')}
              className="flex items-center space-x-2 bg-blue-600 text-white hover:bg-blue-700 border-blue-600"
            >
              <FaFacebookF className="h-4 w-4" />
              <span>{t('facebook')}</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSocialShare('twitter')}
              className="flex items-center space-x-2 bg-blue-400 text-white hover:bg-blue-500 border-blue-400"
            >
              <FaTwitter className="h-4 w-4" />
              <span>{t('twitter')}</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSocialShare('whatsapp')}
              className="flex items-center space-x-2 bg-green-600 text-white hover:bg-green-700 border-green-600"
            >
              <FaWhatsapp className="h-4 w-4" />
              <span>{t('whatsapp')}</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
