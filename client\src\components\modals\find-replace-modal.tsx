import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

interface FindReplaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  onContentChange: (content: string) => void;
}

export default function FindReplaceModal({
  isOpen,
  onClose,
  content,
  onContentChange
}: FindReplaceModalProps) {
  const { t } = useTranslation();
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [matchCase, setMatchCase] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [matches, setMatches] = useState<number[]>([]);

  useEffect(() => {
    if (findText && content) {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      const plainText = tempDiv.textContent || tempDiv.innerText || '';
      
      const regex = new RegExp(
        findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        matchCase ? 'g' : 'gi'
      );
      
      const newMatches: number[] = [];
      let match;
      while ((match = regex.exec(plainText)) !== null) {
        newMatches.push(match.index);
      }
      
      setMatches(newMatches);
      setCurrentIndex(newMatches.length > 0 ? 0 : -1);
    } else {
      setMatches([]);
      setCurrentIndex(-1);
    }
  }, [findText, content, matchCase]);

  const handleFindNext = () => {
    if (matches.length > 0) {
      const nextIndex = (currentIndex + 1) % matches.length;
      setCurrentIndex(nextIndex);
    }
  };

  const handleReplaceAll = () => {
    if (findText && content) {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      const plainText = tempDiv.textContent || tempDiv.innerText || '';
      
      const regex = new RegExp(
        findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        matchCase ? 'g' : 'gi'
      );
      
      const newPlainText = plainText.replace(regex, replaceText);
      
      // For simplicity, we'll replace the entire content
      // In a real implementation, you'd want to preserve HTML formatting
      onContentChange(newPlainText);
      
      setFindText('');
      setReplaceText('');
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleFindNext();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('findReplace')}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="find-input" className="text-sm font-medium">
              {t('find')}
            </Label>
            <Input
              id="find-input"
              type="text"
              value={findText}
              onChange={(e) => setFindText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={t('find') + '...'}
              className="mt-1"
              autoFocus
            />
          </div>
          
          <div>
            <Label htmlFor="replace-input" className="text-sm font-medium">
              {t('replaceWith')}
            </Label>
            <Input
              id="replace-input"
              type="text"
              value={replaceText}
              onChange={(e) => setReplaceText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={t('replaceWith') + '...'}
              className="mt-1"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="match-case"
              checked={matchCase}
              onCheckedChange={(checked) => setMatchCase(checked as boolean)}
            />
            <Label htmlFor="match-case" className="text-sm">
              {t('matchCase')}
            </Label>
          </div>
          
          {matches.length > 0 && (
            <div className="text-sm text-muted-foreground">
              {currentIndex + 1} of {matches.length} matches
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            {t('cancel')}
          </Button>
          <Button 
            variant="outline" 
            onClick={handleFindNext}
            disabled={matches.length === 0}
          >
            {t('findNext')}
          </Button>
          <Button 
            onClick={handleReplaceAll}
            disabled={!findText || matches.length === 0}
          >
            {t('replaceAll')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
