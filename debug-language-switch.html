<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switch Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .test-button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>🔧 Language Switch Debug Tool</h1>
    
    <div class="debug-info">
        <h3>当前状态</h3>
        <div id="current-status"></div>
    </div>

    <div class="debug-info">
        <h3>测试语言切换</h3>
        <button class="test-button" onclick="testLanguageSwitch('en')">Test English</button>
        <button class="test-button" onclick="testLanguageSwitch('zh')">Test 中文</button>
        <button class="test-button" onclick="testLanguageSwitch('es')">Test Español</button>
        <button class="test-button" onclick="testLanguageSwitch('fr')">Test Français</button>
        <button class="test-button" onclick="testLanguageSwitch('de')">Test Deutsch</button>
    </div>

    <div class="debug-info">
        <h3>测试结果</h3>
        <div id="test-results"></div>
    </div>

    <script>
        function updateStatus() {
            const statusDiv = document.getElementById('current-status');
            statusDiv.innerHTML = `
                <strong>当前时间:</strong> ${new Date().toLocaleString()}<br>
                <strong>服务器状态:</strong> <span id="server-status">检查中...</span>
            `;
            
            // 检查服务器状态
            fetch('http://localhost:5000/')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('server-status').innerHTML = '<span style="color: green;">✅ 运行中</span>';
                    } else {
                        document.getElementById('server-status').innerHTML = '<span style="color: red;">❌ 响应异常</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('server-status').innerHTML = '<span style="color: red;">❌ 无法连接</span>';
                });
        }

        function testLanguageSwitch(lang) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            let testUrl;
            if (lang === 'en') {
                testUrl = 'http://localhost:5000/';
            } else {
                testUrl = `http://localhost:5000/${lang}/`;
            }
            
            resultsDiv.innerHTML += `<div class="result">
                [${timestamp}] 测试 ${lang}: <a href="${testUrl}" target="_blank">${testUrl}</a>
            </div>`;
            
            // 测试URL是否可访问
            fetch(testUrl)
                .then(response => {
                    if (response.ok) {
                        resultsDiv.innerHTML += `<div class="result success">
                            [${timestamp}] ✅ ${lang} 页面加载成功
                        </div>`;
                    } else {
                        resultsDiv.innerHTML += `<div class="result error">
                            [${timestamp}] ❌ ${lang} 页面响应错误: ${response.status}
                        </div>`;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML += `<div class="result error">
                        [${timestamp}] ❌ ${lang} 页面加载失败: ${error.message}
                    </div>`;
                });
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // 页面加载时更新状态
        updateStatus();
        
        // 每5秒更新一次状态
        setInterval(updateStatus, 5000);
    </script>

    <div style="margin-top: 20px;">
        <button class="test-button" onclick="clearResults()" style="background: #6c757d;">清除结果</button>
        <button class="test-button" onclick="updateStatus()" style="background: #28a745;">刷新状态</button>
    </div>

    <div class="debug-info">
        <h3>📝 调试说明</h3>
        <ul>
            <li>点击语言测试按钮会在新标签页中打开对应的语言页面</li>
            <li>检查页面是否正常加载，内容是否为对应语言</li>
            <li>在页面中测试语言切换器功能</li>
            <li>观察URL变化是否正确</li>
        </ul>
    </div>
</body>
</html>
