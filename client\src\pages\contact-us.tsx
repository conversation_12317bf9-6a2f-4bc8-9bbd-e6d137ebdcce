import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, MessageCircle, Clock, Globe, ArrowLeft } from 'lucide-react';
import { Link, useLocation } from 'wouter';
import SEOHead from '@/components/seo-head';

export default function ContactUs() {
  const { t, i18n } = useTranslation();
  const [location] = useLocation();

  // 获取当前语言的首页路径
  const getHomeUrl = () => {
    const currentLang = i18n.language;
    if (currentLang === 'en') {
      return '/';
    }
    return `/${currentLang}/`;
  };

  return (
    <>
      <SEOHead 
        title={t('contactUs')}
        description={t('contactUsDescription')}
        path="/contact-us"
      />
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          {/* Back Button */}
          <div className="mb-6">
            <Link href={getHomeUrl()}>
              <Button variant="ghost" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-4 w-4" />
                <span>{t('backToHome')}</span>
              </Button>
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8 sm:mb-12">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {t('contactUs')}
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('contactUsSubtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Information */}
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-primary" />
                  <span>{t('getInTouch')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start space-x-3">
                  <Mail className="h-5 w-5 text-gray-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{t('email')}</h3>
                    <a 
                      href="mailto:<EMAIL>"
                      className="text-primary hover:text-primary/80 transition-colors"
                    >
                      <EMAIL>
                    </a>
                    <p className="text-sm text-gray-500 mt-1">
                      {t('emailDescription')}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-gray-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{t('responseTime')}</h3>
                    <p className="text-gray-600">{t('responseTimeDescription')}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Globe className="h-5 w-5 text-gray-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{t('languages')}</h3>
                    <p className="text-gray-600">{t('languagesDescription')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Form Alternative */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5 text-primary" />
                  <span>{t('howCanWeHelp')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">{t('generalInquiries')}</h4>
                    <p className="text-blue-700 text-sm">
                      {t('generalInquiriesDescription')}
                    </p>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 mb-2">{t('technicalSupport')}</h4>
                    <p className="text-green-700 text-sm">
                      {t('technicalSupportDescription')}
                    </p>
                  </div>

                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900 mb-2">{t('featureRequests')}</h4>
                    <p className="text-purple-700 text-sm">
                      {t('featureRequestsDescription')}
                    </p>
                  </div>

                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h4 className="font-medium text-orange-900 mb-2">{t('businessInquiries')}</h4>
                    <p className="text-orange-700 text-sm">
                      {t('businessInquiriesDescription')}
                    </p>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600 text-center">
                    {t('contactUsFooterNote')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Information */}
          <div className="mt-8 sm:mt-12">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {t('whyContactUs')}
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <MessageCircle className="h-6 w-6 text-blue-600" />
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{t('quickResponse')}</h4>
                      <p className="text-sm text-gray-600">{t('quickResponseDescription')}</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Globe className="h-6 w-6 text-green-600" />
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{t('multilingualSupport')}</h4>
                      <p className="text-sm text-gray-600">{t('multilingualSupportDescription')}</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Mail className="h-6 w-6 text-purple-600" />
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{t('personalizedHelp')}</h4>
                      <p className="text-sm text-gray-600">{t('personalizedHelpDescription')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
