import { useEffect, useRef, useCallback } from 'react';
import { LocalStorage, type NoteData } from '@/lib/storage';

interface UseAutoSaveOptions {
  content: string;
  title?: string;
  interval?: number;
  onSave?: (status: 'saving' | 'saved' | 'error') => void;
}

export function useAutoSave({ content, title, interval = 1000, onSave }: UseAutoSaveOptions) {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastContentRef = useRef<string>('');

  const saveNote = useCallback(() => {
    if (content === lastContentRef.current) return;

    onSave?.('saving');
    
    try {
      const noteData: NoteData = {
        content,
        title,
        timestamp: new Date().toISOString()
      };
      
      LocalStorage.saveNote(noteData);
      lastContentRef.current = content;
      onSave?.('saved');
    } catch (error) {
      console.error('Auto-save failed:', error);
      onSave?.('error');
    }
  }, [content, title, onSave]);

  const debouncedSave = useCallback(() => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(saveNote, interval);
  }, [saveNote, interval]);

  useEffect(() => {
    if (content !== lastContentRef.current) {
      debouncedSave();
    }

    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [content, debouncedSave]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);

  return {
    saveNow: saveNote
  };
}
