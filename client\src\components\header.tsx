import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { supportedLanguages, generateLanguageUrl, detectLanguageFromPath } from '@/lib/i18n';

interface HeaderProps {
  saveStatus: 'saved' | 'saving' | 'error';
  lastSavedTime: Date | null;
}

export default function Header({ saveStatus, lastSavedTime }: HeaderProps) {
  const { t, i18n } = useTranslation();
  const [location, setLocation] = useLocation();

  // 从当前路径检测语言
  const getCurrentLanguage = () => {
    const langMatch = location.match(/^\/([a-z]{2})\//);
    if (langMatch) {
      const pathLang = langMatch[1];
      if (supportedLanguages.some(lang => lang.code === pathLang)) {
        return pathLang;
      }
    }
    return 'en';
  };

  const currentLanguage = getCurrentLanguage();

  const handleLanguageChange = (languageCode: string) => {
    let newPath = location;

    // 移除现有的语言前缀
    const cleanPath = location.replace(/^\/[a-z]{2}\//, '/');

    // 添加新的语言前缀（英文不需要前缀）
    if (languageCode === 'en') {
      newPath = cleanPath;
    } else {
      newPath = `/${languageCode}${cleanPath}`;
    }

    // 确保路径以 / 开头
    if (!newPath.startsWith('/')) {
      newPath = '/' + newPath;
    }

    setLocation(newPath);
    i18n.changeLanguage(languageCode);
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'saved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'saving':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getSaveStatusText = () => {
    switch (saveStatus) {
      case 'saved':
        return t('saved');
      case 'saving':
        return t('saving');
      case 'error':
        return t('error');
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14 sm:h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <div className="flex items-center space-x-2">
              <img src="/ainotes-logo-192.png" alt="AiNotes Logo" className="h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0" />
              <h1 className="text-lg sm:text-xl font-bold text-foreground truncate">{t('appName')}</h1>
            </div>
            <span className="text-muted-foreground text-xs sm:text-sm hidden md:inline">
              {t('subtitle')}
            </span>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3">
                  <Globe className="h-4 w-4" />
                  <span className="hidden lg:inline text-xs sm:text-sm">
                    {supportedLanguages.find(lang => lang.code === currentLanguage)?.nativeName}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-36">
                {supportedLanguages.map((language) => (
                  <DropdownMenuItem
                    key={language.code}
                    onClick={() => handleLanguageChange(language.code)}
                    className={currentLanguage === language.code ? 'bg-accent' : ''}
                  >
                    {language.nativeName}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Save Status */}
            <div className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
              <div className="flex items-center space-x-1">
                {getSaveStatusIcon()}
                <span className="text-muted-foreground hidden sm:inline">{getSaveStatusText()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
