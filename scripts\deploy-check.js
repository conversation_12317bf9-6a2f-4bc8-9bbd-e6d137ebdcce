#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 检查部署准备情况...\n');

const checks = [
  {
    name: '检查 vercel.json 配置',
    check: () => fs.existsSync('vercel.json'),
    fix: '请确保 vercel.json 文件存在'
  },
  {
    name: '检查构建输出目录',
    check: () => fs.existsSync('dist') && fs.existsSync('dist/index.js') && fs.existsSync('dist/public'),
    fix: '请运行 npm run build 生成构建文件'
  },
  {
    name: '检查 package.json 中的 vercel-build 脚本',
    check: () => {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return pkg.scripts && pkg.scripts['vercel-build'];
    },
    fix: '请在 package.json 中添加 vercel-build 脚本'
  },
  {
    name: '检查 .vercelignore 文件',
    check: () => fs.existsSync('.vercelignore'),
    fix: '建议创建 .vercelignore 文件以优化部署'
  }
];

let allPassed = true;

checks.forEach((check, index) => {
  const passed = check.check();
  const status = passed ? '✅' : '❌';
  console.log(`${index + 1}. ${status} ${check.name}`);
  
  if (!passed) {
    console.log(`   💡 ${check.fix}`);
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 所有检查通过！项目已准备好部署到 Vercel');
  console.log('\n📋 下一步：');
  console.log('1. 确保代码已推送到 Git 仓库');
  console.log('2. 在 Vercel 中导入项目');
  console.log('3. 配置环境变量（DATABASE_URL 等）');
  console.log('4. 部署完成后运行数据库迁移');
} else {
  console.log('⚠️  请修复上述问题后再进行部署');
  process.exit(1);
}
