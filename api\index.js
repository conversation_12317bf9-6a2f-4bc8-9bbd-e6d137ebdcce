// Vercel serverless function entry point
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the built Express app
const appPath = path.resolve(__dirname, '../dist/vercel.js');

let app;

export default async function handler(req, res) {
  if (!app) {
    try {
      // Set Vercel environment variable
      process.env.VERCEL = '1';
      process.env.NODE_ENV = 'production';

      // Dynamic import the built Express app
      const appModule = await import(appPath);
      let appExport = appModule.default || appModule.app || appModule;

      // If it's a Promise, await it
      if (appExport && typeof appExport.then === 'function') {
        app = await appExport;
      } else {
        app = appExport;
      }

      if (typeof app !== 'function') {
        throw new Error('Invalid app export - expected Express app function');
      }
    } catch (error) {
      console.error('Failed to import Express app:', error);
      return res.status(500).json({
        error: 'Internal server error',
        details: error.message
      });
    }
  }

  // Handle the request with Express
  try {
    return app(req, res);
  } catch (error) {
    console.error('Error handling request:', error);
    return res.status(500).json({
      error: 'Request handling error',
      details: error.message
    });
  }
}
