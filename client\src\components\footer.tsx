import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'wouter';
import { Check } from 'lucide-react';
import { FaFacebookF, FaTwitter, FaLinkedinIn } from 'react-icons/fa';

export default function Footer() {
  const { t } = useTranslation();

  const features = [
    'richTextEditing',
    'autoSave',
    'wordCount',
    'findReplace',
    'multiLanguage',
    'printPdfExport'
  ];

  const supportLinks = [
    'privacyPolicy',
    'termsOfService',
    'contactUs'
  ];

  return (
    <footer className="bg-muted border-t border-gray-200 mt-8 sm:mt-12">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {/* Brand Section */}
          <div className="col-span-1 sm:col-span-2 lg:col-span-2">
            <div className="flex items-center space-x-2 mb-3 sm:mb-4">
              <img src="/ainotes-logo-192.png" alt="AiNotes Logo" className="h-5 w-5 sm:h-6 sm:w-6" />
              <span className="text-base sm:text-lg font-bold text-foreground">{t('appName')}</span>
            </div>
            <p className="text-muted-foreground mb-3 sm:mb-4 max-w-md text-sm sm:text-base leading-relaxed">
              {t('description')}
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-xs sm:text-sm text-muted-foreground">
                © 2025 {t('appName')}. {t('allRightsReserved')}
              </span>
            </div>
          </div>

          {/* Features */}
          <div className="col-span-1">
            <h3 className="font-semibold text-foreground mb-3 sm:mb-4 text-sm sm:text-base">{t('features')}</h3>
            <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm text-muted-foreground">
              {features.map((feature) => (
                <li key={feature} className="flex items-center space-x-2">
                  <Check className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
                  <span>{t(feature)}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div className="col-span-1">
            <h3 className="font-semibold text-foreground mb-3 sm:mb-4 text-sm sm:text-base">{t('support')}</h3>
            <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm">
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-muted-foreground hover:text-primary transition-colors block py-1"
                >
                  {t('privacyPolicy')}
                </Link>
              </li>
              <li>
                <Link
                  href="/terms-of-service"
                  className="text-muted-foreground hover:text-primary transition-colors block py-1"
                >
                  {t('termsOfService')}
                </Link>
              </li>
              <li>
                <Link
                  href="/contact-us"
                  className="text-muted-foreground hover:text-primary transition-colors block py-1"
                >
                  {t('contactUs')}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 mt-6 sm:mt-8 pt-4 sm:pt-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
              <span>{t('trustedByUsers')}</span>
            </div>
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <span className="text-xs sm:text-sm text-muted-foreground">{t('shareAiNotes')}:</span>
              <div className="flex items-center space-x-3">
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors p-1"
                  aria-label="Share on Facebook"
                >
                  <FaFacebookF className="h-3 w-3 sm:h-4 sm:w-4" />
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors p-1"
                  aria-label="Share on Twitter"
                >
                  <FaTwitter className="h-3 w-3 sm:h-4 sm:w-4" />
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors p-1"
                  aria-label="Share on LinkedIn"
                >
                  <FaLinkedinIn className="h-3 w-3 sm:h-4 sm:w-4" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
