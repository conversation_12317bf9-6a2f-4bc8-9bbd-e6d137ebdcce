import type { Request, Response, NextFunction } from "express";

const SUPPORTED_LANGUAGES = ['en', 'es', 'fr', 'de', 'zh'];
const DEFAULT_LANGUAGE = 'en';

export function languageDetectionMiddleware(req: Request, res: Response, next: NextFunction) {
  const url = req.originalUrl;
  
  // 跳过API路由和静态资源
  if (url.startsWith('/api/') || url.includes('.')) {
    return next();
  }
  
  // 检查URL是否已经包含语言前缀
  const langMatch = url.match(/^\/([a-z]{2})\//);
  if (langMatch && SUPPORTED_LANGUAGES.includes(langMatch[1])) {
    return next();
  }
  
  // 如果是根路径，检查是否需要重定向
  if (url === '/') {
    const acceptLanguage = req.headers['accept-language'];
    const detectedLang = detectLanguageFromHeader(acceptLanguage);
    
    // 如果检测到的语言不是英文，重定向到对应的语言路径
    if (detectedLang !== DEFAULT_LANGUAGE) {
      return res.redirect(302, `/${detectedLang}/`);
    }
  }
  
  next();
}

function detectLanguageFromHeader(acceptLanguage?: string): string {
  if (!acceptLanguage) {
    return DEFAULT_LANGUAGE;
  }
  
  // 解析 Accept-Language 头
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [code, q = '1'] = lang.trim().split(';q=');
      return {
        code: code.split('-')[0].toLowerCase(),
        quality: parseFloat(q)
      };
    })
    .sort((a, b) => b.quality - a.quality);
  
  // 找到第一个支持的语言
  for (const lang of languages) {
    if (SUPPORTED_LANGUAGES.includes(lang.code)) {
      return lang.code;
    }
  }
  
  return DEFAULT_LANGUAGE;
}

export function generateLanguageAlternates(path: string): string[] {
  const alternates = [];
  
  SUPPORTED_LANGUAGES.forEach(lang => {
    const href = lang === 'en' ? `https://ainotes.work${path}` : `https://ainotes.work/${lang}${path}`;
    const hreflang = getHreflangCode(lang);
    alternates.push(`<link rel="alternate" hreflang="${hreflang}" href="${href}" />`);
  });
  
  // 添加 x-default
  alternates.push(`<link rel="alternate" hreflang="x-default" href="https://ainotes.work${path}" />`);
  
  return alternates;
}

function getHreflangCode(lang: string): string {
  const hreflangMap: Record<string, string> = {
    'en': 'en-US',
    'es': 'es-ES',
    'fr': 'fr-FR',
    'de': 'de-DE',
    'zh': 'zh-CN',
  };
  return hreflangMap[lang] || 'en-US';
}
