import { useMemo } from 'react';

export function useWordCount(content: string) {
  const counts = useMemo(() => {
    // Remove HTML tags and get plain text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const plainText = tempDiv.textContent || tempDiv.innerText || '';
    
    // Count words
    const words = plainText.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Count characters
    const characterCount = plainText.length;
    
    return {
      words: wordCount,
      characters: characterCount,
      plainText
    };
  }, [content]);

  return counts;
}
