<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>AI Notes - Free Online Note Taking App | ainotes.work</title>
    <meta name="description" content="AI Notes is a powerful, free online note-taking application with rich text editing, auto-save, and multi-language support. Create, edit, and share notes instantly." />
    <meta name="keywords" content="online notepad, note taking app, rich text editor, auto save notes, free notepad, ai notes, markdown editor, text editor, note organizer" />
    <meta name="author" content="AI Notes" />
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ainotes.work/" />
    <meta property="og:title" content="AI Notes - Free Online Note Taking App" />
    <meta property="og:description" content="AI Notes is a powerful, free online note-taking application with rich text editing, auto-save, and multi-language support. Create, edit, and share notes instantly." />
    <meta property="og:image" content="https://ainotes.work/ainotes-logo-512.png" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image:height" content="512" />
    <meta property="og:image:alt" content="AI Notes Logo" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:site_name" content="AI Notes" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://ainotes.work/" />
    <meta name="twitter:title" content="AI Notes - Free Online Note Taking App" />
    <meta name="twitter:description" content="AI Notes is a powerful, free online note-taking application with rich text editing, auto-save, and multi-language support. Create, edit, and share notes instantly." />
    <meta name="twitter:image" content="https://ainotes.work/ainotes-logo-512.png" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://ainotes.work/" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/ainotes-logo-192.png" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="msapplication-TileColor" content="#ffffff" />

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="AI Notes - Free Online Note Taking App" />
    <meta property="og:description" content="Create, edit, and share notes with our powerful online notepad featuring rich text editing and auto-save functionality." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ainotes.work" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet" />

    <!-- jQuery (required for Summernote) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Summernote JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CM41V5SRET"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-CM41V5SRET');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
