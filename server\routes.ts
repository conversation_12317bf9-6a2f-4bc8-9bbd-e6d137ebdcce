import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertNoteSchema, type Note } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Note sharing endpoints
  app.post("/api/notes", async (req, res) => {
    try {
      const noteData = insertNoteSchema.parse(req.body);
      const note = await storage.createNote(noteData);
      res.json(note);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ message: "Invalid note data", errors: error.errors });
      } else {
        console.error("Failed to create note:", error);
        res.status(500).json({ message: "Failed to create note" });
      }
    }
  });

  app.get("/api/notes/:shareId", async (req, res) => {
    try {
      const { shareId } = req.params;
      const note = await storage.getNoteByShareId(shareId);
      
      if (!note) {
        res.status(404).json({ message: "Note not found" });
        return;
      }
      
      res.json(note);
    } catch (error) {
      console.error("Failed to fetch note:", error);
      res.status(500).json({ message: "Failed to fetch note" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
